version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: artarchiv_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
      - ./docker/php/local.ini:/usr/local/etc/php/conf.d/local.ini
    networks:
      - artarchiv
    depends_on:
      - db
      - redis

  webserver:
    image: nginx:alpine
    container_name: artarchiv_webserver
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - ./:/var/www
      - ./docker/nginx/default.conf:/etc/nginx/conf.d/default.conf
    networks:
      - artarchiv
    depends_on:
      - app

  db:
    image: mysql:8.0
    container_name: artarchiv_db
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: artarchiv
      MYSQL_ROOT_PASSWORD: root
      MYSQL_PASSWORD: password
      MYSQL_USER: artarchiv
      SERVICE_TAGS: dev
      SERVICE_NAME: mysql
    volumes:
      - dbdata:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/my.cnf
    ports:
      - "3306:3306"
    networks:
      - artarchiv

  redis:
    image: redis:alpine
    container_name: artarchiv_redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    networks:
      - artarchiv

  node:
    image: node:18-alpine
    container_name: artarchiv_node
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
    ports:
      - "5173:5173"
    command: sh -c "npm install && npm run dev -- --host 0.0.0.0"
    networks:
      - artarchiv

networks:
  artarchiv:
    driver: bridge

volumes:
  dbdata:
    driver: local
