version: '3.8'

services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: artarchiv_app
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - ./:/var/www
    ports:
      - "8000:8000"
    depends_on:
      - db


  db:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: artarchiv_mssql
    restart: unless-stopped
    environment:
      SA_PASSWORD: "YourStrong@Passw0rd"
      ACCEPT_EULA: "Y"
      MSSQL_PID: "Developer"
    volumes:
      - mssqldata:/var/opt/mssql
    ports:
      - "1433:1433"

volumes:
  mssqldata:
    driver: local
