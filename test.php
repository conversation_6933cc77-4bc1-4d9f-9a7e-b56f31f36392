<?php
/**
 * Server Requirements Test for Laravel + MSSQL Setup
 * Run this file to check if your server meets all requirements
 */

echo "<h1>Server Requirements Test - Laravel + MSSQL</h1>\n";
echo "<style>
    .pass { color: green; font-weight: bold; }
    .fail { color: red; font-weight: bold; }
    .warning { color: orange; font-weight: bold; }
    table { border-collapse: collapse; width: 100%; }
    th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
    th { background-color: #f2f2f2; }
</style>\n";

$requirements = [];
$warnings = [];
$errors = [];

// PHP Version Check
$phpVersion = PHP_VERSION;
$minPhpVersion = '8.2.0';
$phpVersionOk = version_compare($phpVersion, $minPhpVersion, '>=');

$requirements[] = [
    'requirement' => 'PHP Version >= 8.2.0',
    'current' => $phpVersion,
    'status' => $phpVersionOk ? 'PASS' : 'FAIL'
];

if (!$phpVersionOk) {
    $errors[] = "PHP version $phpVersion is below minimum required version $minPhpVersion";
}

// Required PHP Extensions
$requiredExtensions = [
    'pdo' => 'PDO Extension',
    'pdo_sqlsrv' => 'PDO SQL Server Extension',
    'sqlsrv' => 'SQL Server Extension',
    'mbstring' => 'Multibyte String Extension',
    'openssl' => 'OpenSSL Extension',
    'tokenizer' => 'Tokenizer Extension',
    'xml' => 'XML Extension',
    'ctype' => 'Ctype Extension',
    'json' => 'JSON Extension',
    'bcmath' => 'BCMath Extension',
    'fileinfo' => 'Fileinfo Extension',
    'gd' => 'GD Extension'
];

foreach ($requiredExtensions as $ext => $name) {
    $loaded = extension_loaded($ext);
    $requirements[] = [
        'requirement' => $name,
        'current' => $loaded ? 'Loaded' : 'Not Loaded',
        'status' => $loaded ? 'PASS' : 'FAIL'
    ];
    
    if (!$loaded) {
        $errors[] = "Required extension '$ext' is not loaded";
    }
}

// Optional but recommended extensions
$recommendedExtensions = [
    'curl' => 'cURL Extension',
    'zip' => 'Zip Extension',
    'exif' => 'EXIF Extension',
    'pcntl' => 'Process Control Extension'
];

foreach ($recommendedExtensions as $ext => $name) {
    $loaded = extension_loaded($ext);
    $requirements[] = [
        'requirement' => "$name (Recommended)",
        'current' => $loaded ? 'Loaded' : 'Not Loaded',
        'status' => $loaded ? 'PASS' : 'WARNING'
    ];
    
    if (!$loaded) {
        $warnings[] = "Recommended extension '$ext' is not loaded";
    }
}

// PHP Configuration Checks
$phpConfig = [
    'memory_limit' => ['current' => ini_get('memory_limit'), 'recommended' => '256M'],
    'max_execution_time' => ['current' => ini_get('max_execution_time'), 'recommended' => '300'],
    'upload_max_filesize' => ['current' => ini_get('upload_max_filesize'), 'recommended' => '10M'],
    'post_max_size' => ['current' => ini_get('post_max_size'), 'recommended' => '10M']
];

foreach ($phpConfig as $setting => $info) {
    $requirements[] = [
        'requirement' => "PHP $setting",
        'current' => $info['current'],
        'status' => 'INFO'
    ];
}

// Test MSSQL Connection (if available)
if (extension_loaded('pdo_sqlsrv')) {
    try {
        // Try to connect to MSSQL (this will fail if container isn't running, but that's OK)
        $dsn = "sqlsrv:Server=artarchiv_mssql,1433;Database=master";
        $pdo = new PDO($dsn, 'sa', 'YourStrong@Passw0rd');
        $requirements[] = [
            'requirement' => 'MSSQL Connection Test',
            'current' => 'Connected successfully',
            'status' => 'PASS'
        ];
    } catch (PDOException $e) {
        $requirements[] = [
            'requirement' => 'MSSQL Connection Test',
            'current' => 'Connection failed: ' . $e->getMessage(),
            'status' => 'WARNING'
        ];
        $warnings[] = "MSSQL connection test failed (this is normal if containers aren't running)";
    }
}

// Display Results
echo "<table>\n";
echo "<tr><th>Requirement</th><th>Current Value</th><th>Status</th></tr>\n";

foreach ($requirements as $req) {
    $class = strtolower($req['status']);
    echo "<tr>";
    echo "<td>{$req['requirement']}</td>";
    echo "<td>{$req['current']}</td>";
    echo "<td class='$class'>{$req['status']}</td>";
    echo "</tr>\n";
}

echo "</table>\n";

// Summary
echo "<h2>Summary</h2>\n";

if (empty($errors)) {
    echo "<p class='pass'>✓ All required extensions and PHP version requirements are met!</p>\n";
} else {
    echo "<p class='fail'>✗ Some requirements are not met:</p>\n";
    echo "<ul>\n";
    foreach ($errors as $error) {
        echo "<li class='fail'>$error</li>\n";
    }
    echo "</ul>\n";
}

if (!empty($warnings)) {
    echo "<p class='warning'>⚠ Warnings:</p>\n";
    echo "<ul>\n";
    foreach ($warnings as $warning) {
        echo "<li class='warning'>$warning</li>\n";
    }
    echo "</ul>\n";
}

// Laravel-specific checks
echo "<h2>Laravel Environment</h2>\n";

if (file_exists('.env')) {
    echo "<p class='pass'>✓ .env file exists</p>\n";
} else {
    echo "<p class='warning'>⚠ .env file not found (copy .env.docker to .env)</p>\n";
}

if (file_exists('vendor/autoload.php')) {
    echo "<p class='pass'>✓ Composer dependencies installed</p>\n";
} else {
    echo "<p class='fail'>✗ Composer dependencies not installed (run: composer install)</p>\n";
}

if (is_writable('storage')) {
    echo "<p class='pass'>✓ Storage directory is writable</p>\n";
} else {
    echo "<p class='fail'>✗ Storage directory is not writable</p>\n";
}

if (is_writable('bootstrap/cache')) {
    echo "<p class='pass'>✓ Bootstrap cache directory is writable</p>\n";
} else {
    echo "<p class='fail'>✗ Bootstrap cache directory is not writable</p>\n";
}

echo "<hr>\n";
echo "<p><strong>PHP Version:</strong> " . PHP_VERSION . "</p>\n";
echo "<p><strong>Server:</strong> " . $_SERVER['SERVER_SOFTWARE'] ?? 'Unknown' . "</p>\n";
echo "<p><strong>Test completed at:</strong> " . date('Y-m-d H:i:s') . "</p>\n";
?>
