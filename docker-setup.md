# Docker Setup for ArtArchiv Backend

This is a minimal Docker Compose setup for your Laravel API backend with MSSQL Server.

## Services

- **app**: Laravel application (PHP 8.2 with MSSQL drivers)
- **db**: Microsoft SQL Server 2022

## Getting Started

1. **Copy environment file**:
   ```bash
   cp .env.docker .env
   ```

2. **Build and start containers**:
   ```bash
   docker-compose up --build
   ```

3. **Generate application key** (in another terminal):
   ```bash
   docker-compose exec app php artisan key:generate
   ```

4. **Create database** (connect to MSSQL and create database):
   ```bash
   docker-compose exec db /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P "YourStrong@Passw0rd" -C -Q "CREATE DATABASE artarchiv"
   ```

5. **Run migrations**:
   ```bash
   docker-compose exec app php artisan migrate
   ```

## Access

- **Laravel API**: http://localhost:8000
- **MSSQL Server**: localhost:1433
  - Username: `sa`
  - Password: `YourStrong@Passw0rd`

## Development

The application directory is mounted as a volume (`./:/var/www`), so any changes you make to your code will be immediately reflected in the running container.

## Useful Commands

```bash
# View logs
docker-compose logs -f app

# Access app container shell
docker-compose exec app bash

# Access database
docker-compose exec db /opt/mssql-tools18/bin/sqlcmd -S localhost -U sa -P "YourStrong@Passw0rd" -C

# Stop containers
docker-compose down

# Rebuild containers
docker-compose up --build
```
